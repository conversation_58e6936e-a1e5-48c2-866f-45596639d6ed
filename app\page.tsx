"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Search, Plus, Eye, Edit, Trash2, Heart, TestTube, Pill, Microscope, Scan, FileText } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { VitalsSection } from "@/components/sections/vitals-section"
import { LabsSection } from "@/components/sections/labs-section"
import { MedicationsSection } from "@/components/sections/medications-section"
import { CulturesSection } from "@/components/sections/cultures-section"
import { RadiologySection } from "@/components/sections/radiology-section"
import { NotesSection } from "@/components/sections/notes-section"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { ProtectedLayout } from "@/components/protected-layout"
import { dataClient } from "@/lib/data-client"
import type { Patient, UnitType } from "@/lib/types"
import { useToast } from "@/hooks/use-toast"
import { useOnlineStatus } from "@/hooks/use-online-status"

export default function DashboardPage() {
  const [patients, setPatients] = useState<Patient[]>([])
  const [dischargedPatients, setDischargedPatients] = useState<Patient[]>([])
  const [deceasedPatients, setDeceasedPatients] = useState<Patient[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [unitFilter, setUnitFilter] = useState<string>("all")
  const [editingPatient, setEditingPatient] = useState<Patient | null>(null)
  const [isEditOpen, setIsEditOpen] = useState(false)
  const [editForm, setEditForm] = useState({
    patient_id: "",
    name: "",
    main_complaint: "",
    initial_diagnosis: "",
    medical_history: "",
    is_active: true,
    is_discharged: false,
    is_deceased: false,
  })
  const [unitTypes, setUnitTypes] = useState<UnitType[]>([])
  
  const selectStatus = (status: "active" | "discharged" | "deceased") => {
    setEditForm((previous) => ({
      ...previous,
      is_active: status === "active",
      is_discharged: status === "discharged",
      is_deceased: status === "deceased",
    }))
  }
  const router = useRouter()
  const { toast } = useToast()
  const isOnline = useOnlineStatus()
  const [isOfflineDetailOpen, setIsOfflineDetailOpen] = useState(false)
  const [viewingPatient, setViewingPatient] = useState<Patient | null>(null)
  const [isLoadingDischarged, setIsLoadingDischarged] = useState(false)
  const [isLoadingDeceased, setIsLoadingDeceased] = useState(false)
  const [hasLoadedDischarged, setHasLoadedDischarged] = useState(false)
  const [hasLoadedDeceased, setHasLoadedDeceased] = useState(false)

  useEffect(() => {
    loadPatients()
    // Pull server data on mount
    ;(async () => {
      const { syncService } = await import("@/lib/sync")
      syncService.pullServerData()
    })()
  }, [])

  // Load unit types with online/offline strategy
  useEffect(() => {
    const loadUnitTypes = async () => {
      try {
        console.log('Loading units, online status:', isOnline)
        const unitTypesData = await dataClient.getUnitTypes(isOnline)
        console.log('Loaded unit types:', unitTypesData)
        setUnitTypes(unitTypesData)
      } catch (error) {
        console.error('Error loading unit types:', error)
        // Fallback: try to get from local DB only
        try {
          const fallbackUnits = await dataClient.getUnitTypes(false)
          setUnitTypes(fallbackUnits)
        } catch (fallbackError) {
          console.error('Error loading fallback units:', fallbackError)
        }
      }
    }
    
    loadUnitTypes()
  }, [isOnline])

  const loadPatients = async () => {
    try {
      const patientsData = await dataClient.getPatients("active")
      setPatients(patientsData)
    } catch (error) {
      console.error("Error loading patients:", error)
    }
  }

  const loadDischargedPatients = async () => {
    try {
      setIsLoadingDischarged(true)
      const data = await dataClient.getPatients("discharged")
      setDischargedPatients(data)
      setHasLoadedDischarged(true)
    } catch (error) {
      console.error("Error loading discharged patients:", error)
    } finally {
      setIsLoadingDischarged(false)
    }
  }

  const loadDeceasedPatients = async () => {
    try {
      setIsLoadingDeceased(true)
      const data = await dataClient.getPatients("deceased")
      setDeceasedPatients(data)
      setHasLoadedDeceased(true)
    } catch (error) {
      console.error("Error loading deceased patients:", error)
    } finally {
      setIsLoadingDeceased(false)
    }
  }

  const doesPatientMatchFilters = (patient: Patient) => {
    const matchesSearch =
      patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.patient_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.main_complaint?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.diseases.some((disease) => disease.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesUnit = unitFilter === "all" || !unitFilter ? true : patient.unit?.name === unitFilter

    return matchesSearch && matchesUnit
  }

  const filteredActivePatients = patients.filter(doesPatientMatchFilters)
  const filteredDischargedPatients = dischargedPatients.filter(doesPatientMatchFilters)
  const filteredDeceasedPatients = deceasedPatients.filter(doesPatientMatchFilters)

  const PatientCard = ({ patient }: { patient: Patient }) => (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <h3 className="font-semibold text-lg">{patient.name}</h3>
            {/* <p className="text-sm text-muted-foreground">ID: {patient.patient_id}</p> */}
            <p className="text-xs text-muted-foreground">
              Admitted: {new Date(patient.admission_date).toLocaleDateString()}
            </p>
          </div>

          {patient.diseases.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {patient.diseases.slice(0, 3).map((disease) => (
                <Badge key={disease} variant="secondary" className="text-xs">
                  {disease}
                </Badge>
              ))}
              {patient.diseases.length > 6 && (
                <Badge variant="outline" className="text-xs">
                  +{patient.diseases.length - 2} more
                </Badge>
              )}
            </div>
          )}

          {patient.main_complaint && (
            <p className="text-sm line-clamp-2">{patient.main_complaint}</p>
          )}

          <div className="flex items-center pt-2 justify-end">
            <Button variant="ghost" size="sm" asChild>
              <Link
                href={`/patient/${patient.id}`}
                prefetch
                onClick={(e) => {
                  if (!isOnline) {
                    e.preventDefault()
                    setViewingPatient(patient)
                    setIsOfflineDetailOpen(true)
                  }
                }}
              >
                <Eye className="h-4 w-4 mr-1" />
                View
              </Link>
            </Button>

            <div className="flex gap-2">
              <Button variant="ghost" size="sm" onClick={() => handleEditPatient(patient)}>
                <Edit className="h-4 w-4" />
              </Button>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <Trash2 className="h-4 w-4 text-red-600" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Patient</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete {patient.name}? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => handleDeletePatient(patient)}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const handleEditPatient = (patient: Patient) => {
    setEditingPatient(patient)
    const selectedStatus = patient.is_deceased
      ? "deceased"
      : patient.is_discharged
      ? "discharged"
      : "active"
    setEditForm({
      patient_id: patient.patient_id,
      name: patient.name,
      main_complaint: patient.main_complaint || "",
      initial_diagnosis: patient.initial_diagnosis || "",
      medical_history: patient.medical_history || "",
      is_active: selectedStatus === "active",
      is_discharged: selectedStatus === "discharged",
      is_deceased: selectedStatus === "deceased",
    })
    setIsEditOpen(true)
  }

  const handleSaveEdit = async () => {
    if (!editingPatient) return

    try {
      await dataClient.updatePatient(editingPatient.id, editForm)
      await loadPatients()
      setEditingPatient(null)
      setIsEditOpen(false)
      toast({
        title: "Patient updated",
        description: "Patient information has been updated successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update patient information.",
        variant: "destructive",
      })
    }
  }

  const handleDeletePatient = async (patient: Patient) => {
    try {
      await dataClient.deletePatient(patient.id)
      await loadPatients()
      toast({
        title: "Patient deleted",
        description: "Patient has been removed from the system.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete patient.",
        variant: "destructive",
      })
    }
  }

  return (
    <ProtectedLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h1 className="text-3xl font-bold">Patient Directory</h1>
          <Button onClick={() => router.push("/add-patient")}>
            <Plus className="h-4 w-4 mr-2" />
            Add Patient
          </Button>
        </div>

        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search by name, ID, or diagnosis..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="w-full sm:w-44">
            <Select value={unitFilter} onValueChange={(v) => setUnitFilter(v)}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by unit" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Units</SelectItem>
                {unitTypes.length === 0 ? (
                  <SelectItem value="loading" disabled>Loading units...</SelectItem>
                ) : (
                  unitTypes.map((unit) => (
                    <SelectItem key={unit.id} value={unit.name}>
                      {unit.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredActivePatients.map((patient) => (
            <PatientCard key={patient.id} patient={patient} />
          ))}
        </div>

        {filteredActivePatients.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No patients found.</p>
          </div>
        )}

        <Accordion type="multiple" className="mt-8">
          <AccordionItem value="discharged">
            <AccordionTrigger>Discharged Patients</AccordionTrigger>
            <AccordionContent>
              <div className="mb-4">
                <Button onClick={loadDischargedPatients} disabled={isLoadingDischarged}>
                  {isLoadingDischarged ? "Fetching..." : "Fetch"}
                </Button>
              </div>
              {hasLoadedDischarged ? (
                filteredDischargedPatients.length > 0 ? (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {filteredDischargedPatients.map((patient) => (
                      <PatientCard key={patient.id} patient={patient} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <p className="text-muted-foreground">No discharged patients found.</p>
                  </div>
                )
              ) : (
                <p className="text-sm text-muted-foreground">Patients will appear here after fetching.</p>
              )}
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="deceased">
            <AccordionTrigger>Deceased Patients</AccordionTrigger>
            <AccordionContent>
              <div className="mb-4">
                <Button onClick={loadDeceasedPatients} disabled={isLoadingDeceased}>
                  {isLoadingDeceased ? "Fetching..." : "Fetch"}
                </Button>
              </div>
              {hasLoadedDeceased ? (
                filteredDeceasedPatients.length > 0 ? (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {filteredDeceasedPatients.map((patient) => (
                      <PatientCard key={patient.id} patient={patient} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <p className="text-muted-foreground">No deceased patients found.</p>
                  </div>
                )
              ) : (
                <p className="text-sm text-muted-foreground">Patients will appear here after fetching.</p>
              )}
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        {filteredActivePatients.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No patients found.</p>
          </div>
        )}

        {/* Edit Patient Dialog (hoisted to avoid re-mount flicker) */}
        <Dialog
          open={isEditOpen}
          onOpenChange={(open) => {
            setIsEditOpen(open)
            if (!open) setEditingPatient(null)
          }}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Patient</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="patient_id">Patient ID</Label>
                  <Input
                    id="patient_id"
                    value={editForm.patient_id}
                    onChange={(e) => setEditForm({ ...editForm, patient_id: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={editForm.name}
                    onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="main_complaint">Main Complaint</Label>
                <Textarea
                  id="main_complaint"
                  value={editForm.main_complaint}
                  onChange={(e) => setEditForm({ ...editForm, main_complaint: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="initial_diagnosis">Initial Diagnosis</Label>
                <Textarea
                  id="initial_diagnosis"
                  value={editForm.initial_diagnosis}
                  onChange={(e) => setEditForm({ ...editForm, initial_diagnosis: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="medical_history">History</Label>
                <Textarea
                  id="medical_history"
                  value={editForm.medical_history}
                  onChange={(e) => setEditForm({ ...editForm, medical_history: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id={`is_active_${editingPatient?.id ?? "current"}`}
                    checked={editForm.is_active}
                    onCheckedChange={() => selectStatus("active")}
                  />
                  <Label htmlFor={`is_active_${editingPatient?.id ?? "current"}`}>Active</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id={`is_discharged_${editingPatient?.id ?? "current"}`}
                    checked={editForm.is_discharged}
                    onCheckedChange={() => selectStatus("discharged")}
                  />
                  <Label htmlFor={`is_discharged_${editingPatient?.id ?? "current"}`}>Discharged</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id={`is_deceased_${editingPatient?.id ?? "current"}`}
                    checked={editForm.is_deceased}
                    onCheckedChange={() => selectStatus("deceased")}
                  />
                  <Label htmlFor={`is_deceased_${editingPatient?.id ?? "current"}`}>Died</Label>
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsEditOpen(false)
                    setEditingPatient(null)
                  }}
                >
                  Cancel
                </Button>
                <Button onClick={handleSaveEdit}>Save</Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Offline patient detail fallback */}
      <Dialog open={isOfflineDetailOpen} onOpenChange={(open) => {
        setIsOfflineDetailOpen(open)
        if (!open) setViewingPatient(null)
      }}>
        <DialogContent className="w-[95vw] sm:max-w-5xl max-h-[90vh] p-4 sm:p-6 overflow-y-auto">
          {viewingPatient && (
            <div className="space-y-6">
              <DialogHeader>
                <DialogTitle>Patient: {viewingPatient.name}</DialogTitle>
              </DialogHeader>

              <Card>
                <CardContent className="p-3 bg-[#f1f5f9]">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Patient Name:</h3>
                      <p className="text-2xl font-bold">{viewingPatient.name}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Patient ID:</h3>
                      <p className="text-2xl font-bold">{viewingPatient.patient_id}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Admission Date:</h3>
                      <p className="text-lg font-semibold">{new Date(viewingPatient.admission_date).toLocaleDateString()}</p>
                    </div>
                    <div className="space-y-4">
                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Gender</h3>
                          <Badge variant="outline">{viewingPatient.gender}</Badge>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Age</h3>
                          <p className="font-semibold">{viewingPatient.age} years</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Weight</h3>
                          <p className="font-semibold">{viewingPatient.weight} kg</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {viewingPatient.diseases.length > 0 && (
                    <div className="mt-6">
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">Chronic Diseases</h3>
                      <div className="flex flex-wrap gap-2">
                        {viewingPatient.diseases.map((disease) => (
                          <Badge key={disease} variant="secondary">
                            {disease}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Tabs defaultValue="vitals" className="space-y-4 ">
                <TabsList className="grid w-full grid-cols-6 bg-[#f1f5f9]">
                  <TabsTrigger value="vitals" className="flex items-center gap-2">
                    <Heart className="h-4 w-4" />
                    <span className="hidden sm:inline">Vital Signs</span>
                  </TabsTrigger>
                  <TabsTrigger value="labs" className="flex items-center gap-2">
                    <TestTube className="h-4 w-4" />
                    <span className="hidden sm:inline">Lab Values</span>
                  </TabsTrigger>
                  <TabsTrigger value="medications" className="flex items-center gap-2">
                    <Pill className="h-4 w-4" />
                    <span className="hidden sm:inline">Medications</span>
                  </TabsTrigger>
                  <TabsTrigger value="cultures" className="flex items-center gap-2">
                    <Microscope className="h-4 w-4" />
                    <span className="hidden sm:inline">Cultures</span>
                  </TabsTrigger>
                  <TabsTrigger value="radiology" className="flex items-center gap-2">
                    <Scan className="h-4 w-4" />
                    <span className="hidden sm:inline">Radiology</span>
                  </TabsTrigger>
                  <TabsTrigger value="notes" className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    <span className="hidden sm:inline">Doctor Notes</span>
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="vitals">
                  <VitalsSection patientId={viewingPatient.patient_id} />
                </TabsContent>
                <TabsContent value="labs">
                  <LabsSection patientId={viewingPatient.patient_id} />
                </TabsContent>
                <TabsContent value="medications">
                  <MedicationsSection patientId={viewingPatient.patient_id} />
                </TabsContent>
                <TabsContent value="cultures">
                  <CulturesSection patientId={viewingPatient.patient_id} />
                </TabsContent>
                <TabsContent value="radiology">
                  <RadiologySection patientId={viewingPatient.patient_id} />
                </TabsContent>
                <TabsContent value="notes">
                  <NotesSection patientId={viewingPatient.patient_id} />
                </TabsContent>
              </Tabs>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </ProtectedLayout>
  )
}
