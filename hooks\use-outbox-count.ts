"use client"

import { useState, useEffect } from "react"
import { dataClient } from "@/lib/data-client"

export function useOutboxCount() {
  const [count, setCount] = useState(0)

  useEffect(() => {
    const updateCount = async () => {
      const newCount = await dataClient.getOutboxCount()
      setCount(newCount)
    }

    updateCount()

    // Update count every 5 seconds
    const interval = setInterval(updateCount, 5000)

    return () => clearInterval(interval)
  }, [])

  return count
}
