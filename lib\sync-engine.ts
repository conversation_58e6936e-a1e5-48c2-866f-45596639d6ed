"use client"

import { db } from "./db"
import { supabase } from "./supabase"
import type { SyncableRecord, SyncableTableName, SYNC_CONFIG } from "./types"

export interface SyncResult {
  success: boolean
  pushedCount: number
  pulledCount: number
  conflictsResolved: number
  deletedCount: number
  errors: string[]
}

export interface TableSyncResult {
  tableName: string
  result: SyncResult
}

class SyncEngine {
  private isOnline = true
  private isSyncing = false

  constructor() {
    if (typeof window !== "undefined") {
      this.isOnline = navigator.onLine
      window.addEventListener("online", () => {
        this.isOnline = true
      })
      window.addEventListener("offline", () => {
        this.isOnline = false
      })
    }
  }

  /**
   * Sync a single table with bidirectional sync
   */
  async syncTable(tableName: SyncableTableName): Promise<SyncResult> {
    if (!this.isOnline) {
      return {
        success: false,
        pushedCount: 0,
        pulledCount: 0,
        conflictsResolved: 0,
        deletedCount: 0,
        errors: ["Device is offline"]
      }
    }

    const result: SyncResult = {
      success: true,
      pushedCount: 0,
      pulledCount: 0,
      conflictsResolved: 0,
      deletedCount: 0,
      errors: []
    }

    try {
      // Update sync status to indicate syncing
      await db.updateSyncStatus(tableName, { isSyncing: true, error: null })

      // Phase 1: Push dirty local records to remote
      const pushResult = await this.pushDirtyRecords(tableName)
      result.pushedCount = pushResult.count
      result.errors.push(...pushResult.errors)

      // Phase 2: Pull updated records from remote
      const pullResult = await this.pullUpdatedRecords(tableName)
      result.pulledCount = pullResult.count
      result.conflictsResolved = pullResult.conflictsResolved
      result.errors.push(...pullResult.errors)

      // Phase 3: Clean up deleted records
      const cleanupResult = await this.cleanupDeletedRecords(tableName)
      result.deletedCount = cleanupResult.count
      result.errors.push(...cleanupResult.errors)

      // Update sync status
      const now = new Date().toISOString()
      await db.updateSyncStatus(tableName, {
        isSyncing: false,
        lastSyncAt: now,
        error: result.errors.length > 0 ? result.errors.join("; ") : null
      })

      result.success = result.errors.length === 0

    } catch (error) {
      result.success = false
      result.errors.push(error instanceof Error ? error.message : "Unknown error")
      
      await db.updateSyncStatus(tableName, {
        isSyncing: false,
        error: result.errors.join("; ")
      })
    }

    return result
  }

  /**
   * Push all dirty local records to remote database
   */
  private async pushDirtyRecords(tableName: SyncableTableName): Promise<{count: number, errors: string[]}> {
    const errors: string[] = []
    let count = 0

    try {
      const dirtyRecords = await db.getDirtyRecords(tableName)
      
      for (const record of dirtyRecords) {
        try {
          if (record.deleted) {
            // Handle soft deletion
            const { error } = await supabase
              .from(tableName)
              .update({ deleted: true, dirty: false, updated_at: record.updated_at })
              .eq('id', record.id)

            if (error) throw error
          } else {
            // Handle insert/update
            const { error } = await supabase
              .from(tableName)
              .upsert({
                ...record,
                dirty: false // Mark as clean on remote
              })

            if (error) throw error
          }

          // Mark local record as clean
          await db.markRecordClean(tableName, record.id)
          count++

        } catch (error) {
          errors.push(`Failed to push ${record.id}: ${error instanceof Error ? error.message : "Unknown error"}`)
        }
      }

    } catch (error) {
      errors.push(`Failed to get dirty records: ${error instanceof Error ? error.message : "Unknown error"}`)
    }

    return { count, errors }
  }

  /**
   * Pull updated records from remote database and resolve conflicts
   */
  private async pullUpdatedRecords(tableName: SyncableTableName): Promise<{count: number, conflictsResolved: number, errors: string[]}> {
    const errors: string[] = []
    let count = 0
    let conflictsResolved = 0

    try {
      // Get last sync timestamp
      const syncStatus = await db.getSyncStatus(tableName)
      const lastSyncAt = syncStatus?.lastSyncAt || '1970-01-01T00:00:00.000Z'

      // Fetch records updated since last sync
      const { data: remoteRecords, error } = await supabase
        .from(tableName)
        .select('*')
        .gt('updated_at', lastSyncAt)
        .order('updated_at', { ascending: true })

      if (error) throw error

      if (!remoteRecords || remoteRecords.length === 0) {
        return { count: 0, conflictsResolved: 0, errors: [] }
      }

      for (const remoteRecord of remoteRecords) {
        try {
          // @ts-ignore - Dynamic table access
          const localRecord = await db[tableName].get(remoteRecord.id)

          if (!localRecord) {
            // No local record exists, insert remote record
            // @ts-ignore - Dynamic table access
            await db[tableName].add(remoteRecord)
            count++
          } else {
            // Local record exists, check for conflicts
            const remoteUpdatedAt = new Date(remoteRecord.updated_at).getTime()
            const localUpdatedAt = new Date(localRecord.updated_at).getTime()

            if (remoteUpdatedAt > localUpdatedAt) {
              // Remote is newer, accept remote changes (last-write-wins)
              // @ts-ignore - Dynamic table access
              await db[tableName].update(remoteRecord.id, remoteRecord)
              conflictsResolved++
              count++
            } else if (localUpdatedAt > remoteUpdatedAt && localRecord.dirty) {
              // Local is newer and dirty, keep local changes
              // This will be pushed in the next sync cycle
              continue
            } else {
              // Timestamps are equal or local is not dirty, accept remote
              // @ts-ignore - Dynamic table access
              await db[tableName].update(remoteRecord.id, remoteRecord)
              count++
            }
          }

        } catch (error) {
          errors.push(`Failed to process record ${remoteRecord.id}: ${error instanceof Error ? error.message : "Unknown error"}`)
        }
      }

    } catch (error) {
      errors.push(`Failed to pull records: ${error instanceof Error ? error.message : "Unknown error"}`)
    }

    return { count, conflictsResolved, errors }
  }

  /**
   * Clean up records that are marked as deleted and have been synced
   */
  private async cleanupDeletedRecords(tableName: SyncableTableName): Promise<{count: number, errors: string[]}> {
    const errors: string[] = []
    let count = 0

    try {
      const deletedRecords = await db.getDeletedRecords(tableName)
      
      for (const record of deletedRecords) {
        if (!record.dirty) {
          // Record has been synced and is marked for deletion
          try {
            await db.permanentlyDeleteRecord(tableName, record.id)
            count++
          } catch (error) {
            errors.push(`Failed to delete record ${record.id}: ${error instanceof Error ? error.message : "Unknown error"}`)
          }
        }
      }

    } catch (error) {
      errors.push(`Failed to cleanup deleted records: ${error instanceof Error ? error.message : "Unknown error"}`)
    }

    return { count, errors }
  }

  /**
   * Sync all configured tables
   */
  async syncAllTables(): Promise<TableSyncResult[]> {
    if (this.isSyncing) {
      throw new Error("Sync already in progress")
    }

    this.isSyncing = true
    const results: TableSyncResult[] = []

    try {
      // Import SYNC_CONFIG dynamically to avoid circular dependencies
      const { SYNC_CONFIG } = await import('./types')
      
      for (const config of SYNC_CONFIG) {
        if (config.enabled) {
          const result = await this.syncTable(config.tableName as SyncableTableName)
          results.push({
            tableName: config.tableName,
            result
          })
        }
      }

    } finally {
      this.isSyncing = false
    }

    return results
  }

  /**
   * Check if sync is currently in progress
   */
  get syncing(): boolean {
    return this.isSyncing
  }

  /**
   * Check if device is online
   */
  get online(): boolean {
    return this.isOnline
  }
}

export const syncEngine = new SyncEngine()

/**
 * Sync orchestrator that manages sync timing and coordination
 */
class SyncOrchestrator {
  private syncInterval: NodeJS.Timeout | null = null
  private isInitialized = false

  constructor() {
    if (typeof window !== "undefined") {
      this.initialize()
    }
  }

  private initialize() {
    if (this.isInitialized) return
    this.isInitialized = true

    // Start periodic sync when online
    window.addEventListener("online", () => {
      this.startPeriodicSync()
    })

    // Stop periodic sync when offline
    window.addEventListener("offline", () => {
      this.stopPeriodicSync()
    })

    // Start periodic sync if already online
    if (navigator.onLine) {
      this.startPeriodicSync()
    }
  }

  startPeriodicSync() {
    if (this.syncInterval) return

    // Sync every 30 seconds (instead of 1 second like the old outbox)
    this.syncInterval = setInterval(() => {
      this.triggerSync()
    }, 30000)

    // Trigger immediate sync
    this.triggerSync()
  }

  stopPeriodicSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
      this.syncInterval = null
    }
  }

  async triggerSync(): Promise<TableSyncResult[]> {
    if (!syncEngine.online || syncEngine.syncing) {
      return []
    }

    try {
      return await syncEngine.syncAllTables()
    } catch (error) {
      console.error("Sync orchestrator error:", error)
      return []
    }
  }

  /**
   * Trigger sync after local changes (debounced)
   */
  private debounceTimer: NodeJS.Timeout | null = null

  triggerSyncAfterChange() {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
    }

    this.debounceTimer = setTimeout(() => {
      this.triggerSync()
    }, 2000) // Wait 2 seconds after last change
  }

  /**
   * Manual sync trigger for user-initiated sync
   */
  async manualSync(): Promise<TableSyncResult[]> {
    return await syncEngine.syncAllTables()
  }
}

export const syncOrchestrator = new SyncOrchestrator()
