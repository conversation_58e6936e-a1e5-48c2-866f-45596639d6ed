"use client"

import { db } from "./db"
import { dataClient } from "./data-client"
import { syncEngine, syncOrchestrator } from "./sync-engine"
import { syncService } from "./sync"

/**
 * Test suite for the new offline-first sync architecture
 * This file contains basic tests to verify the implementation works correctly
 */

export class SyncArchitectureTest {
  
  /**
   * Test basic CRUD operations with dirty flag handling
   */
  async testCRUDOperations(): Promise<boolean> {
    try {
      console.log("Testing CRUD operations...")

      // Test patient creation
      const newPatient = await dataClient.insertPatient({
        patient_id: "TEST001",
        name: "Test Patient",
        gender: "male",
        age: 30,
        weight: 70,
        admission_date: new Date().toISOString().split('T')[0],
        diseases: []
      })

      // Verify patient was created with correct sync flags
      if (!newPatient.dirty || newPatient.deleted) {
        throw new Error("Patient not created with correct sync flags")
      }

      // Test patient update
      await dataClient.updatePatient(newPatient.id, {
        age: 31
      })

      // Verify patient is still dirty after update
      const updatedPatient = await dataClient.getPatient(newPatient.id)
      if (!updatedPatient?.dirty) {
        throw new Error("Patient not marked dirty after update")
      }

      // Test soft deletion
      await dataClient.deletePatient(newPatient.id)

      // Verify patient is marked as deleted and dirty
      const deletedPatient = await db.patients.get(newPatient.id)
      if (!deletedPatient?.deleted || !deletedPatient?.dirty) {
        throw new Error("Patient not properly soft deleted")
      }

      // Verify patient doesn't appear in normal queries
      const activePatients = await dataClient.getPatients("active")
      const foundDeleted = activePatients.find(p => p.id === newPatient.id)
      if (foundDeleted) {
        throw new Error("Deleted patient still appears in active queries")
      }

      console.log("✅ CRUD operations test passed")
      return true

    } catch (error) {
      console.error("❌ CRUD operations test failed:", error)
      return false
    }
  }

  /**
   * Test sync status tracking
   */
  async testSyncStatus(): Promise<boolean> {
    try {
      console.log("Testing sync status...")

      // Get initial sync status
      const initialStatus = await syncService.getSyncStatus()
      if (!Array.isArray(initialStatus)) {
        throw new Error("Sync status should return an array")
      }

      // Check if all expected tables have sync status
      const expectedTables = [
        'patients', 'vital_signs', 'lab_values', 'medications', 
        'medication_names', 'medication_adherence', 'doctor_notes', 
        'cultures', 'radiology', 'unit_types'
      ]

      for (const tableName of expectedTables) {
        const tableStatus = await syncService.getSyncStatusForTable(tableName)
        if (!tableStatus) {
          throw new Error(`No sync status found for table: ${tableName}`)
        }
      }

      console.log("✅ Sync status test passed")
      return true

    } catch (error) {
      console.error("❌ Sync status test failed:", error)
      return false
    }
  }

  /**
   * Test dirty records detection
   */
  async testDirtyRecordsDetection(): Promise<boolean> {
    try {
      console.log("Testing dirty records detection...")

      // Create a test record
      const testNote = await dataClient.insertDoctorNote({
        patient_id: "TEST001",
        date: new Date().toISOString().split('T')[0],
        content: "Test note for sync testing"
      })

      // Check if dirty records are detected
      const dirtyRecords = await db.getDirtyRecords('doctor_notes')
      const foundDirty = dirtyRecords.find(r => r.id === testNote.id)
      
      if (!foundDirty) {
        throw new Error("Dirty record not detected")
      }

      // Test dirty count
      const dirtyCount = await dataClient.getDirtyRecordsCount()
      if (dirtyCount === 0) {
        throw new Error("Dirty count should be greater than 0")
      }

      console.log("✅ Dirty records detection test passed")
      return true

    } catch (error) {
      console.error("❌ Dirty records detection test failed:", error)
      return false
    }
  }

  /**
   * Test sync engine initialization
   */
  async testSyncEngineInitialization(): Promise<boolean> {
    try {
      console.log("Testing sync engine initialization...")

      // Check if sync engine is properly initialized
      const isOnline = syncEngine.online
      const isSyncing = syncEngine.syncing

      if (typeof isOnline !== 'boolean') {
        throw new Error("Sync engine online status should be boolean")
      }

      if (typeof isSyncing !== 'boolean') {
        throw new Error("Sync engine syncing status should be boolean")
      }

      console.log("✅ Sync engine initialization test passed")
      return true

    } catch (error) {
      console.error("❌ Sync engine initialization test failed:", error)
      return false
    }
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<boolean> {
    console.log("🧪 Starting sync architecture tests...")

    const tests = [
      this.testSyncEngineInitialization(),
      this.testSyncStatus(),
      this.testCRUDOperations(),
      this.testDirtyRecordsDetection(),
    ]

    const results = await Promise.all(tests)
    const allPassed = results.every(result => result === true)

    if (allPassed) {
      console.log("🎉 All sync architecture tests passed!")
    } else {
      console.log("💥 Some sync architecture tests failed!")
    }

    return allPassed
  }

  /**
   * Clean up test data
   */
  async cleanup(): Promise<void> {
    try {
      // Clean up any test records
      const testRecords = await db.patients.where('patient_id').equals('TEST001').toArray()
      for (const record of testRecords) {
        await db.patients.delete(record.id)
      }

      const testNotes = await db.doctor_notes.where('patient_id').equals('TEST001').toArray()
      for (const note of testNotes) {
        await db.doctor_notes.delete(note.id)
      }

      console.log("🧹 Test cleanup completed")
    } catch (error) {
      console.error("❌ Test cleanup failed:", error)
    }
  }
}

// Export a singleton instance for easy testing
export const syncArchitectureTest = new SyncArchitectureTest()

// Auto-run tests in development (optional)
if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
  // Uncomment the line below to auto-run tests on page load
  // setTimeout(() => syncArchitectureTest.runAllTests(), 2000)
}
