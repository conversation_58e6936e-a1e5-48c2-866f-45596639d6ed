import type { UserRole, GenderType, CultureStatus, RadiologyScanType, RadiologyStatus } from './constants'

// Base interface for all syncable records
export interface SyncableRecord {
  id: string
  updated_at: string
  dirty: boolean
  deleted: boolean
}

// Sync configuration for tables
export interface TableSyncConfig {
  tableName: string
  enabled: boolean
  conflictResolution: 'last-write-wins' | 'manual'
}

// Sync status tracking
export interface SyncStatus {
  tableName: string
  lastSyncAt: string | null
  isSyncing: boolean
  error: string | null
}

export interface User {
  id: string
  username: string
  role: UserRole
}

export interface Patient extends SyncableRecord {
  patient_id: string
  name: string
  gender: GenderType
  age: number
  weight: number
  admission_date: string
  unit_id?: number
  unit?: UnitType // Optional joined data (unit_types table still exists)
  main_complaint?: string
  medical_history?: string
  initial_diagnosis?: string
  is_active?: boolean
  is_discharged?: boolean
  is_deceased?: boolean
  diseases: string[]
  created_at: string
}

export interface UnitType {
  id: number // Keep as number for backward compatibility
  name: string
  description?: string
  is_active: boolean
  created_at: string
  updated_at: string
  dirty: boolean
  deleted: boolean
}

export interface VitalSigns extends SyncableRecord {
  patient_id: string
  date: string
  vital_signs_data: {
    heart_rate?: number
    systolic_bp?: number
    diastolic_bp?: number
    temperature?: number
    respiratory_rate?: number
    oxygen_saturation?: number
    cvp?: number
    fluid_balance?: number
    fluid_intake?: number
    fluid_output?: number
    sofa_score?: number
    apache_score?: number
    rifle_score?: number
    on_ventilator: boolean
    on_support: boolean
    on_dialysis: boolean
  }
  created_at: string
}

export interface LabValues extends SyncableRecord {
  patient_id: string
  date: string
  lab_data: Record<string, any>
  created_at: string
}

export interface Medication extends SyncableRecord {
  patient_id: string
  medication_name: string
  dosage?: string
  frequency?: string
  date_prescribed: string
  is_active: boolean
  created_at: string
}

export interface MedicationName extends SyncableRecord {
  name: string
  created_at: string
}

export interface MedicationAdherence extends SyncableRecord {
  patient_id: string
  medication_id: string
  date: string
  is_taking_medication: boolean
  created_at: string
}

export interface DoctorNote extends SyncableRecord {
  patient_id: string
  date: string
  content: string
  doctor_name?: string
  created_at: string
}

export interface Culture extends SyncableRecord {
  patient_id: string
  requested_date: string
  result_date?: string
  culture_type?: string
  results?: string
  microorganism?: string
  status: CultureStatus
  created_at: string
}

export interface Radiology extends SyncableRecord {
  patient_id: string
  scan_type: RadiologyScanType
  scan_date: string
  body_part?: string
  findings?: string
  status: RadiologyStatus
  created_at: string
}

export interface OutboxOperation {
  id: string
  table_name: string
  operation: "insert" | "update" | "delete"
  data: any
  local_id?: string
  server_id?: string
  created_at: string
  retries: number
  error?: string
}

export interface IdMapping {
  local_id: string
  server_id: string
  table_name: string
}

// Sync configuration for all syncable tables
export const SYNC_CONFIG: TableSyncConfig[] = [
  { tableName: 'patients', enabled: true, conflictResolution: 'last-write-wins' },
  { tableName: 'vital_signs', enabled: true, conflictResolution: 'last-write-wins' },
  { tableName: 'lab_values', enabled: true, conflictResolution: 'last-write-wins' },
  { tableName: 'medications', enabled: true, conflictResolution: 'last-write-wins' },
  { tableName: 'medication_names', enabled: true, conflictResolution: 'last-write-wins' },
  { tableName: 'medication_adherence', enabled: true, conflictResolution: 'last-write-wins' },
  { tableName: 'doctor_notes', enabled: true, conflictResolution: 'last-write-wins' },
  { tableName: 'cultures', enabled: true, conflictResolution: 'last-write-wins' },
  { tableName: 'radiology', enabled: true, conflictResolution: 'last-write-wins' },
  { tableName: 'unit_types', enabled: true, conflictResolution: 'last-write-wins' },
]

// Helper type for table names
export type SyncableTableName = typeof SYNC_CONFIG[number]['tableName']
