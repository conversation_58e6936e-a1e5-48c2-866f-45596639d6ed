-- Clean up existing database objects (for fresh start)
-- Drop all tables with CASCAD<PERSON> to handle dependencies
DROP TABLE IF EXISTS radiology CASCADE;
DROP TABLE IF EXISTS cultures CASCADE;
DROP TABLE IF EXISTS doctor_notes CASCADE;
DROP TABLE IF EXISTS medication_adherence CASCADE;
DROP TABLE IF <PERSON><PERSON>ISTS medications CASCADE;
DROP TABLE IF EXISTS medication_names CASCADE;
DROP TABLE IF EXISTS lab_values CASCADE;
DROP TABLE IF EXISTS vital_signs CASCADE;
DROP TABLE IF EXISTS patients CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Drop reference tables (these will be replaced with hardcoded constants)
DROP TABLE IF EXISTS user_roles CASCADE;
DROP TABLE IF EXISTS culture_statuses CASCADE;
DROP TABLE IF EXISTS radiology_scan_types CASCADE;
DROP TABLE IF EXISTS radiology_statuses CASCADE;
DROP TABLE IF EXISTS gender_types CASCADE;
DROP TABLE IF EXISTS unit_types CASCADE;

-- Drop functions
DROP FUNCTION IF EXISTS login(TEXT, TEXT);
DROP FUNCTION IF EXISTS create_user(TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS update_user_password(UUID, TEXT);

-- Drop old enum types if they exist
DROP TYPE IF EXISTS user_role CASCADE;
DROP TYPE IF EXISTS culture_status CASCADE;
DROP TYPE IF EXISTS radiology_scan_type CASCADE;
DROP TYPE IF EXISTS radiology_status CASCADE;
DROP TYPE IF EXISTS gender_type CASCADE;
DROP TYPE IF EXISTS unit_type CASCADE;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create function to automatically update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create unit_types first since it's referenced by patients table
CREATE TABLE unit_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW(),
    dirty BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE
);

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'user', -- Direct string value instead of FK
    created_at TIMESTAMP DEFAULT NOW()
);

-- Patients table
CREATE TABLE patients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    gender VARCHAR(50) NOT NULL, -- Direct string value instead of FK
    age INTEGER CHECK (age >= 1 AND age <= 150),
    weight DECIMAL(5,2) CHECK (weight > 0),
    admission_date DATE DEFAULT CURRENT_DATE,
    unit_id INTEGER REFERENCES unit_types(id),
    main_complaint TEXT,
    medical_history TEXT,
    initial_diagnosis TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_discharged BOOLEAN DEFAULT FALSE,
    is_deceased BOOLEAN DEFAULT FALSE,
    diseases JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    dirty BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE
);

-- Vital signs table
CREATE TABLE vital_signs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) REFERENCES patients(patient_id) ON DELETE CASCADE,
    date DATE NOT NULL,
    vital_signs_data JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    dirty BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    UNIQUE(patient_id, date)
);

-- Create indexes for better performance on JSONB queries
CREATE INDEX idx_vital_signs_data_gin ON vital_signs USING GIN (vital_signs_data);
CREATE INDEX idx_vital_signs_patient_date ON vital_signs (patient_id, date);

-- Add comment to document the new structure
COMMENT ON COLUMN vital_signs.vital_signs_data IS 'JSONB object containing all vital sign measurements and status indicators';

-- Lab values table
CREATE TABLE lab_values (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) REFERENCES patients(patient_id) ON DELETE CASCADE,
    date DATE NOT NULL,
    lab_data JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    dirty BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    UNIQUE(patient_id, date)
);

-- Medication names catalog
CREATE TABLE medication_names (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    dirty BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE
);

-- Medications table
CREATE TABLE medications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) REFERENCES patients(patient_id) ON DELETE CASCADE,
    medication_name VARCHAR(100) NOT NULL,
    dosage VARCHAR(50),
    frequency VARCHAR(50),
    date_prescribed DATE DEFAULT CURRENT_DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    dirty BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE
);

-- Medication adherence table
CREATE TABLE medication_adherence (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) REFERENCES patients(patient_id) ON DELETE CASCADE,
    medication_id UUID REFERENCES medications(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    is_taking_medication BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    dirty BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    UNIQUE(patient_id, medication_id, date)
);

-- Doctor notes table
CREATE TABLE doctor_notes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) REFERENCES patients(patient_id) ON DELETE CASCADE,
    date DATE DEFAULT CURRENT_DATE,
    content TEXT NOT NULL,
    doctor_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    dirty BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE
);

-- Cultures table
CREATE TABLE cultures (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) REFERENCES patients(patient_id) ON DELETE CASCADE,
    requested_date DATE NOT NULL,
    result_date DATE,
    culture_type VARCHAR(100),
    results TEXT,
    microorganism VARCHAR(100),
    status VARCHAR(50) NOT NULL DEFAULT 'awaiting results', -- Direct string value instead of FK
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    dirty BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE
);

-- Radiology table
CREATE TABLE radiology (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) REFERENCES patients(patient_id) ON DELETE CASCADE,
    scan_type VARCHAR(50) NOT NULL, -- Direct string value instead of FK
    scan_date DATE NOT NULL,
    body_part VARCHAR(100),
    findings TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'scheduled', -- Direct string value instead of FK
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    dirty BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE
);

-- Sync metadata table for tracking sync status
CREATE TABLE sync_metadata (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(50) UNIQUE NOT NULL,
    last_sync_at TIMESTAMP,
    is_syncing BOOLEAN DEFAULT FALSE,
    error TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create triggers to automatically update updated_at column
CREATE TRIGGER update_unit_types_updated_at BEFORE UPDATE ON unit_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_patients_updated_at BEFORE UPDATE ON patients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vital_signs_updated_at BEFORE UPDATE ON vital_signs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_lab_values_updated_at BEFORE UPDATE ON lab_values FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_medication_names_updated_at BEFORE UPDATE ON medication_names FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_medications_updated_at BEFORE UPDATE ON medications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_medication_adherence_updated_at BEFORE UPDATE ON medication_adherence FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_doctor_notes_updated_at BEFORE UPDATE ON doctor_notes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cultures_updated_at BEFORE UPDATE ON cultures FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_radiology_updated_at BEFORE UPDATE ON radiology FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sync_metadata_updated_at BEFORE UPDATE ON sync_metadata FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (removed lookup tables)
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE vital_signs ENABLE ROW LEVEL SECURITY;
ALTER TABLE lab_values ENABLE ROW LEVEL SECURITY;
ALTER TABLE medications ENABLE ROW LEVEL SECURITY;
ALTER TABLE medication_names ENABLE ROW LEVEL SECURITY;
ALTER TABLE medication_adherence ENABLE ROW LEVEL SECURITY;
ALTER TABLE doctor_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE cultures ENABLE ROW LEVEL SECURITY;
ALTER TABLE radiology ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_metadata ENABLE ROW LEVEL SECURITY;

-- Create permissive policies (removed lookup table policies)
CREATE POLICY "Allow all operations" ON patients FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON vital_signs FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON lab_values FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON medications FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON medication_names FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON medication_adherence FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON doctor_notes FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON cultures FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON radiology FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON sync_metadata FOR ALL USING (true);

-- Create login function
CREATE OR REPLACE FUNCTION login(username_input TEXT, password_input TEXT)
RETURNS JSON AS $$
DECLARE
    user_record RECORD;
    role_name TEXT;
    is_valid BOOLEAN;
BEGIN
    SELECT u.*, u.role as role_name 
    INTO user_record 
    FROM users u 
    WHERE u.username = username_input;
    
    IF user_record IS NULL THEN
        RETURN json_build_object('success', false, 'message', 'Invalid credentials');
    END IF;
    
    -- In production, use proper password hashing
    is_valid := user_record.password_hash = crypt(password_input, user_record.password_hash);
    
    IF is_valid THEN
        RETURN json_build_object(
            'success', true,
            'user', json_build_object(
                'id', user_record.id,
                'username', user_record.username,
                'role', user_record.role_name
            )
        );
    ELSE
        RETURN json_build_object('success', false, 'message', 'Invalid credentials');
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create user function for admin
CREATE OR REPLACE FUNCTION create_user(
    username_input TEXT,
    password_input TEXT,
    role_input TEXT DEFAULT 'user'
)
RETURNS JSON AS $$
DECLARE
    new_user_id UUID;
    role_id_val INTEGER;
BEGIN
    -- Check if username already exists
    IF EXISTS (SELECT 1 FROM users WHERE username = username_input) THEN
        RETURN json_build_object('success', false, 'message', 'Username already exists');
    END IF;
    
    -- Validate role input
    IF role_input NOT IN ('admin', 'user') THEN
        role_input := 'user'; -- Default to 'user' role
    END IF;
    
    -- Insert new user
    INSERT INTO users (username, password_hash, role)
    VALUES (username_input, crypt(password_input, gen_salt('bf')), role_input)
    RETURNING id INTO new_user_id;
    
    RETURN json_build_object(
        'success', true,
        'message', 'User created successfully',
        'user_id', new_user_id
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object('success', false, 'message', 'Failed to create user: ' || SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update user password function
CREATE OR REPLACE FUNCTION update_user_password(
    user_id UUID,
    new_password TEXT
)
RETURNS JSON AS $$
BEGIN
    UPDATE users 
    SET password_hash = crypt(new_password, gen_salt('bf'))
    WHERE id = user_id;
    
    IF FOUND THEN
        RETURN json_build_object('success', true, 'message', 'Password updated successfully');
    ELSE
        RETURN json_build_object('success', false, 'message', 'User not found');
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object('success', false, 'message', 'Failed to update password: ' || SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION login(TEXT, TEXT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION create_user(TEXT, TEXT, TEXT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION update_user_password(UUID, TEXT) TO anon, authenticated;

-- Enable RLS and policies for unit_types
ALTER TABLE unit_types ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow all operations" ON unit_types FOR ALL USING (true);

-- Seed unit_types data
INSERT INTO unit_types (id, name) VALUES 
(1, 'ICU A'),
(2, 'ICU B')
ON CONFLICT (name) DO NOTHING;

-- Seed admin user (password: admin123)
INSERT INTO users (username, password_hash, role) 
VALUES ('admin', crypt('admin123', gen_salt('bf')), 'admin')
ON CONFLICT (username) DO NOTHING;

-- Seed some medication names
INSERT INTO medication_names (name) VALUES
('Paracetamol'), ('Ibuprofen'), ('Aspirin'), ('Metformin'), ('Lisinopril'),
('Amlodipine'), ('Simvastatin'), ('Omeprazole'), ('Salbutamol'), ('Prednisolone')
ON CONFLICT (name) DO NOTHING;

-- Initialize sync metadata for all tables
INSERT INTO sync_metadata (table_name) VALUES
('unit_types'), ('patients'), ('vital_signs'), ('lab_values'),
('medication_names'), ('medications'), ('medication_adherence'),
('doctor_notes'), ('cultures'), ('radiology')
ON CONFLICT (table_name) DO NOTHING;
